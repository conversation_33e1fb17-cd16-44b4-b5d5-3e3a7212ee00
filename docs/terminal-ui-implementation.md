# Terminal UI Implementation

This document describes the implementation of the blessed-based terminal UI for the StalkAPI monitoring application.

## Overview

The application uses the `blessed` library to create a terminal-based user interface with multiple panels for displaying different types of information.

## UI Layout

The interface is divided into several sections:

1. **Top Left (60% width, 50% height)**: Main content area
2. **Top Right (40% width, 50% height)**: Console log display  
3. **Bottom (100% width, 50% height)**: Additional information panels

## Console Logging System

The console logging system provides a way to display JSON objects and messages in a format similar to `console.log()` but optimized for the terminal interface.

### Features

- **JSON Object Display**: Shows JSON objects in a compact, readable format
- **Automatic Scrolling**: New messages automatically scroll to the bottom
- **Message Buffering**: Keeps a limited number of recent messages (30 max)
- **Timestamp Display**: Each message includes a timestamp
- **Debounced Updates**: Prevents rendering conflicts with rapid updates

### Implementation

The console system uses a `blessed.box` element with debounced content management to prevent rendering artifacts:

```javascript
// Create console box
this.consoleBox = blessed.box({
  width: "40%",
  height: "50%",
  scrollable: true,
  alwaysScroll: true,
  wrap: false,
  // ... other configuration
});

// Custom log function with debounced updates
log(...args) {
  const timestamp = new Date().toLocaleTimeString();
  let message = `[${timestamp}] `;
  
  // Process arguments with compact JSON formatting
  const processedArgs = args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      // Compact JSON formatting for terminal display
      const compact = JSON.stringify(arg);
      if (compact.length <= 60) {
        return compact;
      }
      // Show key structure for larger objects
      const keys = Object.keys(arg);
      if (keys.length <= 4) {
        return `{${keys.join(', ')}}`;
      }
      return `{${keys.slice(0, 3).join(', ')}, +${keys.length - 3} more}`;
    }
    return String(arg);
  });
  
  message += processedArgs.join(' ');
  this.logBuffer.push(message);
  this.scheduleUpdate(); // Debounced update
}

// Debounced update system
scheduleUpdate() {
  if (this.updatePending) return;
  this.updatePending = true;
  
  setImmediate(() => {
    this.consoleBox.setContent(this.logBuffer.join('\n'));
    this.consoleBox.setScrollPerc(100);
    this.screen.render();
    this.updatePending = false;
  });
}
```

### JSON Display Format

The console displays JSON objects in a compact format that shows the actual structure:

- **Simple objects**: `{"type":"connected","status":"active"}`
- **Complex objects**: `{type, connectionId, sessionId, message}`
- **Large objects**: `{type, data, timestamp, +5 more}`

This approach provides the readability of `console.log()` while fitting within the terminal interface constraints.

## Key Components

### Screen Management

The main screen is created using blessed's screen functionality with proper key bindings for exit functionality.

### Box Elements

Each UI section is implemented as a blessed box element with appropriate styling and positioning.

### Event Handling

The UI handles keyboard events for navigation and application control.

### Rendering Optimization

To prevent blessed rendering conflicts with rapid updates:

1. **Debounced Updates**: Uses `setImmediate()` to batch content updates
2. **Limited Buffer**: Keeps only 30 recent messages for performance
3. **Compact Formatting**: Optimizes JSON display for terminal width
4. **Single Render Calls**: Prevents multiple simultaneous render operations

## Usage

The UI is initialized when the application starts and provides real-time display of:

- Application status messages
- WebSocket connection events  
- Incoming data from the StalkAPI
- JSON object contents in readable format

## Styling

The interface uses a consistent color scheme with cyan borders and appropriate contrast for readability in terminal environments.

## Troubleshooting

### Rendering Issues

If you experience garbled characters or rendering artifacts:

1. Ensure the debounced update system is working properly
2. Check that only one render operation occurs at a time
3. Verify the message buffer size is reasonable (≤30 messages)
4. Confirm JSON formatting stays within width constraints

### Performance Issues

For better performance:

1. Limit the log buffer size
2. Use compact JSON formatting
3. Implement proper debouncing for rapid updates
4. Avoid frequent screen.render() calls
