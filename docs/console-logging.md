# Console Logging Feature

## Overview

The blessed terminal application now includes a dedicated console logging feature that displays logs in the top-right box of the interface. This provides a real-time view of application logs similar to a browser's developer console.

## Features

- **Real-time logging**: Messages appear instantly in the console box
- **Automatic timestamping**: Each log entry includes a timestamp
- **JSON object support**: Objects are automatically formatted and displayed
- **Auto-scrolling**: The console automatically scrolls to show the latest messages
- **Message history**: Keeps the last 100 messages in memory
- **Keyboard shortcuts**: Interactive controls for better user experience

## Usage

### Basic Logging

```javascript
// Simple string logging
app.log("Application started successfully!");

// Multiple arguments
app.log("Server status:", "running", "on port", 3000);
```

### JSON Object Logging

```javascript
const jsonObject = {
  user: "john_doe",
  status: "active",
  permissions: ["read", "write"],
  metadata: {
    lastLogin: new Date().toISOString(),
    sessionCount: 42
  }
};

app.log("User data:", jsonObject);
```

### Array Logging

```javascript
const arrayData = [1, 2, 3, "hello", { nested: "object" }];
app.log("Array data:", arrayData);
```

## Automatic Features

### DateTime Logging
The console automatically logs the current date and time every second to provide a real-time clock reference.

### Global Access
The log function is available globally as `log()` for convenience:

```javascript
log("This works too!");
```

## Keyboard Shortcuts

- **`q` or `Ctrl+C`**: Quit the application
- **`c`**: Clear the console log
- **`l`**: Focus on the console log box
- **`r`**: Refresh the console display (fixes rendering issues)

## Technical Details

### Console Box Configuration
- **Position**: Top-right (60% from left, 0% from top)
- **Size**: 40% width, 50% height
- **Scrollable**: Yes, with mouse and keyboard support
- **Auto-scroll**: Always scrolls to show latest messages
- **Border**: Cyan colored line border with "Console Log" label

### Message Format
Each log entry follows this format:
```
[HH:MM:SS AM/PM] message content
```

### Memory Management
- Keeps only the last 100 messages to prevent memory issues
- Older messages are automatically removed when the limit is exceeded

### Display Optimization
- **Compact Object Display**: Large objects are shown as `{Object}` to prevent line wrapping issues
- **Message Splitting**: Long messages are automatically split into multiple lines
- **No Word Wrapping**: Disabled word wrapping to prevent blessed terminal rendering artifacts
- **Smart JSON Formatting**: JSON objects are compacted to show key information without overwhelming the display

## Implementation Details

The console logging feature is implemented in the `App` class with the following key methods:

- `log(...args)`: Main logging function that accepts multiple arguments
- `updateConsoleDisplay()`: Updates the visual display of the console
- `setupConsoleLogging()`: Initializes the logging system
- `startDateTimeLogging()`: Starts the automatic datetime logging
- `setupKeyboardShortcuts()`: Configures keyboard controls

## Example Output

```
[11:07:59 AM] Current time: 6/17/2025, 11:07:59 AM
[11:08:00 AM] Application started successfully!
[11:08:00 AM] Server status: running on port 3000
[11:08:00 AM] User data: {
  "user": "john_doe",
  "status": "active",
  "permissions": [
    "read",
    "write"
  ],
  "metadata": {
    "lastLogin": "2025-06-17T04:08:00.567Z",
    "sessionCount": 42
  }
}
```

## Integration with StalkApi Module

The console logging system integrates seamlessly with the custom StalkApi module:

```javascript
import { TestWebsocket } from "stalk-api";

// Use the app's logger for websocket events
const testWs = new TestWebsocket(app.log.bind(app));
testWs.start();
```

This will display all websocket events (connections, messages, errors) directly in the blessed console box with proper formatting and timestamps.
