import dotenv from "dotenv";
dotenv.config();

import App from "./src/app.js";

const app = new App();
app.start();

// Example usage of the console logging functionality
setTimeout(() => {
  // Example 1: Simple string logging
  app.log("Application started successfully!");

  // Example 2: Logging with multiple arguments
  app.log("Server status:", "running", "on port", 3000);

  // Example 3: Logging JSON objects
  const jsonObject = {
    user: "john_doe",
    status: "active",
    permissions: ["read", "write"],
    metadata: {
      lastLogin: new Date().toISOString(),
      sessionCount: 42
    }
  };
  app.log("User data:", jsonObject);

  // Example 4: Logging arrays
  const arrayData = [1, 2, 3, "hello", { nested: "object" }];
  app.log("Array data:", arrayData);

}, 2000); // Wait 2 seconds before showing examples

// Handle graceful shutdown
process.on('SIGINT', () => {
  app.log("Shutting down application...");
  setTimeout(() => {
    app.stop();
    process.exit(0);
  }, 1000);
});