import dotenv from "dotenv";
dotenv.config();

import App from "./src/app.js";
import Stalk<PERSON>pi, { TestWebsocket } from "stalk-api";

const app = new App();
app.start();

// Initialize StalkApi with the app's logger
const stalkApi = new StalkApi({
  logger: app.log.bind(app)
});

// Example usage of the console logging functionality
setTimeout(() => {
  // Example 1: Simple string logging
  app.log("Application started successfully!");

  // Example 2: StalkApi configuration check
  app.log("StalkApi configuration:", stalkApi.getConfig());

  // Example 3: Logging JSON objects
  const jsonObject = {
    user: "john_doe",
    status: "active",
    permissions: ["read", "write"],
    metadata: {
      lastLogin: new Date().toISOString(),
      sessionCount: 42
    }
  };
  app.log("User data:", jsonObject);

  // Example 4: TestWebsocket demonstration
  app.log("Creating TestWebsocket instance...");

  // Create TestWebsocket in demo mode (safe to run without real credentials)
  const testWs = new TestWebsocket(app.log.bind(app), { demoMode: false });

  // Start the demo websocket
  app.log("Starting TestWebsocket in demo mode...");
  testWs.start();

  // For real websocket connection, use:
  // const realTestWs = new TestWebsocket(app.log.bind(app));
  // realTestWs.start();

}, 2000); // Wait 2 seconds before showing examples

// Handle graceful shutdown
process.on('SIGINT', () => {
  app.log("Shutting down application...");
  setTimeout(() => {
    app.stop();
    process.exit(0);
  }, 1000);
});