import dotenv from "dotenv";
dotenv.config();

import App from "../src/app.js";
import { TestWebsocket } from "stalk-api";

// Create the blessed app instance
const app = new App();
app.start();

// Create TestWebsocket instance with the app's logger
const testWs = new TestWebsocket(app.log.bind(app));

// Log initial information
app.log("StalkApi Test Starting...");
app.log("Environment check:");
app.log("- STALKAPI_HOST:", process.env.STALKAPI_HOST || "NOT SET");
app.log("- STALK_API_KEY:", process.env.STALK_API_KEY ? "SET" : "NOT SET");

// Start the test websocket after a short delay
setTimeout(() => {
  app.log("Starting TestWebsocket connection...");
  testWs.start();
}, 2000);

// Send a test message every 10 seconds
setInterval(() => {
  if (testWs.getStatus() === 'open') {
    testWs.sendMessage({
      type: 'heartbeat',
      timestamp: new Date().toISOString(),
      message: 'Periodic test message from StalkApi client'
    });
  }
}, 10000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  app.log("Shutting down StalkApi test...");
  testWs.stop();
  setTimeout(() => {
    app.stop();
    process.exit(0);
  }, 1000);
});
