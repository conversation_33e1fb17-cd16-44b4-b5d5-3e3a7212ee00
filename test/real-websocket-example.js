import dotenv from "dotenv";
dotenv.config();

import App from "../src/app.js";
import { TestWebsocket } from "stalk-api";

// Create the blessed app instance
const app = new App();
app.start();

// Example of how to use the real websocket connection
// Make sure you have STALKAPI_HOST and STALK_API_KEY set in your .env file

app.log("Real WebSocket Connection Example");
app.log("=================================");

// Check if environment variables are set
if (!process.env.STALKAPI_HOST || !process.env.STALK_API_KEY) {
  app.log("❌ Missing environment variables!");
  app.log("Please set STALKAPI_HOST and STALK_API_KEY in your .env file");
  app.log("Example .env file:");
  app.log("STALKAPI_HOST=data.stalkapi.com");
  app.log("STALK_API_KEY=your-api-key-here");
} else {
  app.log("✅ Environment variables found");
  app.log("Host:", process.env.STALKAPI_HOST);
  app.log("API Key:", process.env.STALK_API_KEY.substring(0, 8) + "***");
  
  // Create real websocket connection (no demo mode)
  setTimeout(() => {
    app.log("Creating real WebSocket connection...");
    const realWs = new TestWebsocket(app.log.bind(app)); // No demoMode option
    
    app.log("Starting real WebSocket connection...");
    realWs.start();
    
    // Send a custom message after connection is established
    setTimeout(() => {
      if (realWs.getStatus() === 'open') {
        realWs.sendMessage({
          type: 'subscribe',
          stream: 'kol_activity',
          timestamp: new Date().toISOString()
        });
      }
    }, 5000);
    
  }, 2000);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  app.log("Shutting down real websocket example...");
  setTimeout(() => {
    app.stop();
    process.exit(0);
  }, 1000);
});
