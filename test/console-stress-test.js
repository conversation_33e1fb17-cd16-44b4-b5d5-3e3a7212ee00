import dotenv from "dotenv";
dotenv.config();

import App from "../src/app.js";

// Create the blessed app instance
const app = new App();
app.start();

app.log("Console Stress Test Starting...");
app.log("This will test the console with various message types");

// Test different types of messages
setTimeout(() => {
  app.log("=== Testing Simple Messages ===");
  app.log("Simple string message");
  app.log("Message with", "multiple", "arguments");
  app.log("Message with numbers:", 123, 456.789);
}, 1000);

setTimeout(() => {
  app.log("=== Testing Object Messages ===");
  
  // Small object
  app.log("Small object:", { name: "test", value: 42 });
  
  // Medium object
  app.log("Medium object:", {
    user: "john_doe",
    status: "active",
    permissions: ["read", "write", "admin"],
    metadata: { created: new Date().toISOString() }
  });
  
  // Large object (should be compacted)
  app.log("Large object:", {
    id: "12345",
    name: "Very Long Name That Should Be Truncated",
    description: "This is a very long description that contains a lot of text and should be handled properly by the console logging system",
    data: {
      nested: {
        deeply: {
          values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
          moreData: "Even more data here"
        }
      }
    },
    timestamps: {
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      accessed: new Date().toISOString()
    }
  });
}, 3000);

setTimeout(() => {
  app.log("=== Testing Array Messages ===");
  app.log("Small array:", [1, 2, 3]);
  app.log("Large array:", Array.from({length: 20}, (_, i) => `item-${i}`));
  app.log("Mixed array:", [1, "string", { obj: true }, [1, 2, 3]]);
}, 5000);

setTimeout(() => {
  app.log("=== Testing Long Messages ===");
  app.log("This is a very long message that should be properly split into multiple lines to prevent the blessed terminal from having rendering issues with line wrapping and scrolling problems that we experienced before");
}, 7000);

// Simulate rapid logging
setTimeout(() => {
  app.log("=== Rapid Logging Test ===");
  for (let i = 0; i < 10; i++) {
    setTimeout(() => {
      app.log(`Rapid message ${i + 1}:`, { counter: i, timestamp: Date.now() });
    }, i * 200);
  }
}, 9000);

// Test keyboard shortcuts info
setTimeout(() => {
  app.log("=== Keyboard Shortcuts ===");
  app.log("Press 'c' to clear console");
  app.log("Press 'l' to focus console");
  app.log("Press 'r' to refresh display");
  app.log("Press 'q' or Ctrl+C to quit");
}, 12000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  app.log("Shutting down console stress test...");
  setTimeout(() => {
    app.stop();
    process.exit(0);
  }, 1000);
});
