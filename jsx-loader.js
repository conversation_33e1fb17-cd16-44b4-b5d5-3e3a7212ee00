import { transformSync } from 'esbuild';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';

export async function load(url, context, defaultLoad) {
  if (url.endsWith('.js') || url.endsWith('.jsx')) {
    const filePath = fileURLToPath(url);
    try {
      const source = readFileSync(filePath, 'utf8');
      
      // Check if file contains JSX
      if (source.includes('<') && source.includes('>')) {
        const result = transformSync(source, {
          loader: 'jsx',
          format: 'esm',
          jsx: 'automatic',
        });
        
        return {
          format: 'module',
          source: result.code,
        };
      }
    } catch (error) {
      // Fall back to default loader if transformation fails
    }
  }
  
  return defaultLoad(url, context, defaultLoad);
}
