import { StalkWebSocket } from "./websocket.js";

export class TestWebsocket {
  constructor(logger = console.log, options = {}) {
    this.logger = logger;
    this.ws = null;
    this.demoMode = options.demoMode || false; // For testing without real connection
  }

  async start() {
    try {
      // Demo mode for testing without real connection
      if (this.demoMode) {
        this.logger('TestWebsocket: 🎭 Running in DEMO mode (no real connection)');
        this.startDemoMode();
        return true;
      }

      // Validate environment variables
      if (!process.env.STALKAPI_HOST) {
        this.logger(
          "TestWebsocket: ERROR - STALKAPI_HOST environment variable is not set"
        );
        this.logger(
          "TestWebsocket: Please set STALKAPI_HOST in your .env file"
        );
        return false;
      }

      if (!process.env.STALK_API_KEY) {
        this.logger(
          "TestWebsocket: ERROR - STALK_API_KEY environment variable is not set"
        );
        this.logger(
          "TestWebsocket: Please set STALK_API_KEY in your .env file"
        );
        return false;
      }

      const wsUrl = `wss://${process.env.STALKAPI_HOST}/ws`;

      this.logger("TestWebsocket: Initializing connection...");
      this.logger("TestWebsocket: Host:", process.env.STALKAPI_HOST);
      this.logger("TestWebsocket: URL:", wsUrl);
      this.logger(
        "TestWebsocket: API Key:",
        process.env.STALK_API_KEY.substring(0, 8) + "***"
      );

      // Create websocket instance with safer settings
      this.ws = new StalkWebSocket({
        url: wsUrl,
        apiKey: process.env.STALK_API_KEY,
        logger: this.logger,
        maxReconnectAttempts: 2, // Reduced to avoid spam
        reconnectDelay: 3000,
      });

      // Set up event handlers
      this.setupEventHandlers();

      // Start connection
      this.logger("TestWebsocket: Attempting to connect...");
      this.ws.connect();

      return true;
    } catch (error) {
      this.logger("TestWebsocket: Failed to start:", error.message);
      return false;
    }
  }

  setupEventHandlers() {
    this.ws.onOpen = () => {
      this.logger("TestWebsocket: ✅ Connection established successfully");

      // Send a test message after connection
      setTimeout(() => {
        this.sendTestMessage();
      }, 1000);
    };

    this.ws.onMessage = (message) => {
      try {
        // Create a clean, compact message representation
        let messageInfo = this.createMessageSummary(message);

        this.logger("TestWebsocket: 📨 Message received");
        this.logger("TestWebsocket:", messageInfo);

        // Handle different message types safely
        if (message && typeof message === 'object' && message.type) {
          switch (message.type) {
            case "welcome":
              this.logger("TestWebsocket: ✅ Welcome message received");
              break;
            case "connected":
              this.logger("TestWebsocket: 🔗 Connection established");
              break;
            case "subscribed":
              this.logger("TestWebsocket: 📡 Subscribed to stream:", message.stream || 'unknown');
              break;
            case "stream_data":
              const stream = message.stream || 'unknown';
              this.logger(`TestWebsocket: 📊 Stream data from ${stream}`);
              if (message.data && message.data.kol_label) {
                this.logger(`TestWebsocket: 👤 ${message.data.kol_label} (${message.data.type || 'unknown'})`);
              }
              break;
            case "ping":
              this.logger("TestWebsocket: 🏓 Ping received, sending pong");
              this.ws.send({ type: "pong", timestamp: Date.now() });
              break;
            case "error":
              this.logger("TestWebsocket: ❌ Server error:", message.error || 'Unknown error');
              break;
            case "text":
              this.logger("TestWebsocket: 💬 Text message:", message.content);
              break;
            default:
              this.logger("TestWebsocket: ❓ Unknown message type:", message.type);
          }
        }
      } catch (error) {
        this.logger("TestWebsocket: ⚠️ Error processing message:", error.message);
      }
    };

    this.ws.onClose = (code, reason) => {
      this.logger("TestWebsocket: ❌ Connection closed");
      this.logger("TestWebsocket: Close code:", code);
      this.logger("TestWebsocket: Close reason:", reason.toString());
    };

    this.ws.onError = (error) => {
      this.logger("TestWebsocket: ⚠️ Error occurred:", error.message);
    };
  }

  sendTestMessage() {
    if (this.ws && this.ws.isOpen) {
      const testMessage = {
        type: "subscribe",
        payload: {
          stream: "kol-feed",
        },
      };

      this.logger("TestWebsocket: 📤 Sending test message");
      this.ws.send(testMessage);
    } else {
      this.logger(
        "TestWebsocket: Cannot send test message - connection not open"
      );
    }
  }

  // Send custom message
  sendMessage(data) {
    if (this.ws && this.ws.isOpen) {
      this.logger("TestWebsocket: 📤 Sending custom message:", data);
      this.ws.send(data);
    } else {
      this.logger("TestWebsocket: Cannot send message - connection not open");
    }
  }

  // Get connection status
  getStatus() {
    if (!this.ws) {
      return "not_initialized";
    }

    switch (this.ws.readyState) {
      case 0:
        return "connecting";
      case 1:
        return "open";
      case 2:
        return "closing";
      case 3:
        return "closed";
      default:
        return "unknown";
    }
  }

  // Stop the test websocket
  stop() {
    this.logger("TestWebsocket: Stopping test websocket");
    if (this.ws) {
      this.ws.close();
    }
  }

  // Create a compact message summary for display
  createMessageSummary(message) {
    if (typeof message !== 'object' || message === null) {
      return String(message);
    }

    if (message.type) {
      switch (message.type) {
        case 'welcome':
          return `Welcome: ${message.message || 'Connected'}`;
        case 'connected':
          return 'Connection established';
        case 'subscribed':
          return `Subscribed to ${message.stream || 'stream'}`;
        case 'stream_data':
          const stream = message.stream || 'unknown';
          const dataInfo = message.data ? `(${Object.keys(message.data).length} fields)` : '';
          return `Stream data from ${stream} ${dataInfo}`;
        case 'heartbeat':
          return `Heartbeat: ${message.status || 'alive'}`;
        case 'data':
          const payload = message.payload || {};
          const keys = Object.keys(payload).slice(0, 2);
          return `Data: ${keys.join(', ')}${Object.keys(payload).length > 2 ? '...' : ''}`;
        case 'notification':
          return `Notification: ${message.message || 'N/A'} (${message.level || 'info'})`;
        case 'error':
          return `Error: ${message.error || message.message || 'Unknown error'}`;
        default:
          return `${message.type}: ${Object.keys(message).length} fields`;
      }
    }

    // For objects without type, show key count
    const keys = Object.keys(message);
    return `Object with ${keys.length} fields: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`;
  }

  // Demo mode for testing without real connection
  startDemoMode() {
    this.logger("TestWebsocket: 🎭 Starting demo mode...");

    // Simulate connection events
    setTimeout(() => {
      this.logger("TestWebsocket: ✅ Demo connection established");
    }, 1000);

    setTimeout(() => {
      this.logger("TestWebsocket: 📨 Demo message received");
      this.logger("TestWebsocket: Welcome: Connected to demo mode");
    }, 2000);

    setTimeout(() => {
      this.logger("TestWebsocket: 📤 Demo message sent");
    }, 3000);

    // Send periodic demo messages with clean formatting
    setInterval(() => {
      const demoMessages = [
        "Heartbeat: alive",
        "Data: price, symbol (DEMO)",
        "Notification: Demo notification (info)"
      ];

      const randomMessage = demoMessages[Math.floor(Math.random() * demoMessages.length)];
      this.logger("TestWebsocket: 📨 Demo message received");
      this.logger("TestWebsocket:", randomMessage);
    }, 10000);
  }
}
