import { StalkWebSocket } from './websocket.js';

export class TestWebsocket {
  constructor(logger = console.log) {
    this.logger = logger;
    this.ws = null;
  }

  async start() {
    // Validate environment variables
    if (!process.env.STALKAPI_HOST) {
      this.logger('TestWebsocket: ERROR - STALKAPI_HOST environment variable is not set');
      return;
    }

    if (!process.env.STALK_API_KEY) {
      this.logger('TestWebsocket: ERROR - STALK_API_KEY environment variable is not set');
      return;
    }

    const wsUrl = `wss://${process.env.STALKAPI_HOST}/ws`;
    
    this.logger('TestWebsocket: Initializing test websocket connection');
    this.logger('TestWebsocket: Host:', process.env.STALKAPI_HOST);
    this.logger('TestWebsocket: API Key:', process.env.STALK_API_KEY.substring(0, 8) + '...');

    // Create websocket instance
    this.ws = new StalkWebSocket({
      url: wsUrl,
      apiKey: process.env.STALK_API_KEY,
      logger: this.logger,
      maxReconnectAttempts: 3,
      reconnectDelay: 5000
    });

    // Set up event handlers
    this.setupEventHandlers();

    // Start connection
    this.ws.connect();
  }

  setupEventHandlers() {
    this.ws.onOpen = () => {
      this.logger('TestWebsocket: ✅ Connection established successfully');
      
      // Send a test message after connection
      setTimeout(() => {
        this.sendTestMessage();
      }, 1000);
    };

    this.ws.onMessage = (message) => {
      this.logger('TestWebsocket: 📨 Message received:', message);
      
      // Handle different message types
      if (message.type) {
        switch (message.type) {
          case 'welcome':
            this.logger('TestWebsocket: Welcome message received');
            break;
          case 'ping':
            this.logger('TestWebsocket: Ping received, sending pong');
            this.ws.send({ type: 'pong', timestamp: Date.now() });
            break;
          case 'error':
            this.logger('TestWebsocket: Server error:', message.error);
            break;
          default:
            this.logger('TestWebsocket: Unknown message type:', message.type);
        }
      }
    };

    this.ws.onClose = (code, reason) => {
      this.logger('TestWebsocket: ❌ Connection closed');
      this.logger('TestWebsocket: Close code:', code);
      this.logger('TestWebsocket: Close reason:', reason.toString());
    };

    this.ws.onError = (error) => {
      this.logger('TestWebsocket: ⚠️ Error occurred:', error.message);
    };
  }

  sendTestMessage() {
    if (this.ws && this.ws.isOpen) {
      const testMessage = {
        type: 'test',
        message: 'Hello from StalkApi test client',
        timestamp: new Date().toISOString(),
        clientInfo: {
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        }
      };

      this.logger('TestWebsocket: 📤 Sending test message');
      this.ws.send(testMessage);
    } else {
      this.logger('TestWebsocket: Cannot send test message - connection not open');
    }
  }

  // Send custom message
  sendMessage(data) {
    if (this.ws && this.ws.isOpen) {
      this.logger('TestWebsocket: 📤 Sending custom message:', data);
      this.ws.send(data);
    } else {
      this.logger('TestWebsocket: Cannot send message - connection not open');
    }
  }

  // Get connection status
  getStatus() {
    if (!this.ws) {
      return 'not_initialized';
    }

    switch (this.ws.readyState) {
      case 0: return 'connecting';
      case 1: return 'open';
      case 2: return 'closing';
      case 3: return 'closed';
      default: return 'unknown';
    }
  }

  // Stop the test websocket
  stop() {
    this.logger('TestWebsocket: Stopping test websocket');
    if (this.ws) {
      this.ws.close();
    }
  }
}
