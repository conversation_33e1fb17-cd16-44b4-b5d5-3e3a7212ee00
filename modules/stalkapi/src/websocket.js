import WebSocket from 'ws';

export class StalkWebSocket {
  constructor(options = {}) {
    this.url = options.url;
    this.apiKey = options.apiKey;
    this.logger = options.logger || console.log;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectDelay = options.reconnectDelay || 3000;
    this.isConnecting = false;
    this.shouldReconnect = true;
  }

  connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      this.logger('WebSocket: Already connected or connecting');
      return;
    }

    this.isConnecting = true;
    const wsUrl = `${this.url}?apiKey=${this.apiKey}`;

    // Clean log message without exposing API key
    const safeUrl = `${this.url}?apiKey=***`;
    this.logger('WebSocket: Connecting to', safeUrl);

    try {
      this.ws = new WebSocket(wsUrl, {
        // Add some basic options to help with connection
        handshakeTimeout: 10000,
        perMessageDeflate: false
      });
      this.setupEventHandlers();
    } catch (error) {
      this.logger('WebSocket: Connection failed:', error.message);
      this.isConnecting = false;
      this.handleReconnect();
    }
  }

  setupEventHandlers() {
    this.ws.on('open', () => {
      this.logger('WebSocket: Connection opened successfully');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.onOpen && this.onOpen();
    });

    this.ws.on('message', (data) => {
      try {
        const rawMessage = data.toString();
        // Try to parse as JSON first
        let message;
        try {
          message = JSON.parse(rawMessage);
        } catch (parseError) {
          // If not JSON, treat as plain text
          message = { type: 'text', content: rawMessage };
        }

        this.logger('WebSocket: Message received:', message);
        this.onMessage && this.onMessage(message);
      } catch (error) {
        this.logger('WebSocket: Error handling message:', error.message);
      }
    });

    this.ws.on('close', (code, reason) => {
      this.logger('WebSocket: Connection closed', { code, reason: reason.toString() });
      this.isConnecting = false;
      this.onClose && this.onClose(code, reason);
      
      if (this.shouldReconnect) {
        this.handleReconnect();
      }
    });

    this.ws.on('error', (error) => {
      this.logger('WebSocket: Error occurred:', error.message);
      this.isConnecting = false;
      this.onError && this.onError(error);
    });

    this.ws.on('ping', (data) => {
      this.logger('WebSocket: Ping received');
      this.ws.pong(data);
    });

    this.ws.on('pong', () => {
      this.logger('WebSocket: Pong received');
    });
  }

  handleReconnect() {
    if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        this.logger('WebSocket: Max reconnection attempts reached. Giving up.');
      }
      return;
    }

    this.reconnectAttempts++;
    this.logger(`WebSocket: Reconnecting in ${this.reconnectDelay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay);
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.ws.send(message);
      this.logger('WebSocket: Message sent:', data);
    } else {
      this.logger('WebSocket: Cannot send message - connection not open');
    }
  }

  close() {
    this.shouldReconnect = false;
    if (this.ws) {
      this.logger('WebSocket: Closing connection');
      this.ws.close();
    }
  }

  // Event handler setters
  onOpen(callback) {
    this.onOpen = callback;
  }

  onMessage(callback) {
    this.onMessage = callback;
  }

  onClose(callback) {
    this.onClose = callback;
  }

  onError(callback) {
    this.onError = callback;
  }

  // Getters
  get readyState() {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED;
  }

  get isOpen() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }
}
