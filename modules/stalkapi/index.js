// Main StalkApi module exports
export { StalkWebSocket } from './src/websocket.js';
export { TestWebsocket } from './src/test.js';

// Default export - main StalkApi class
export default class StalkApi {
  constructor(options = {}) {
    this.host = options.host || process.env.STALKAPI_HOST;
    this.apiKey = options.apiKey || process.env.STALK_API_KEY;
    this.logger = options.logger || console.log;
  }

  // Create a new websocket connection
  createWebSocket(options = {}) {
    return new StalkWebSocket({
      url: `wss://${this.host}/ws`,
      apiKey: this.apiKey,
      logger: this.logger,
      ...options
    });
  }

  // Create a test websocket instance
  createTestWebSocket() {
    return new TestWebsocket(this.logger);
  }

  // Validate configuration
  validateConfig() {
    const issues = [];
    
    if (!this.host) {
      issues.push('STALKAPI_HOST is not configured');
    }
    
    if (!this.apiKey) {
      issues.push('STALK_API_KEY is not configured');
    }

    if (issues.length > 0) {
      this.logger('StalkApi: Configuration issues found:', issues);
      return false;
    }

    this.logger('StalkApi: Configuration validated successfully');
    return true;
  }

  // Get current configuration (without exposing sensitive data)
  getConfig() {
    return {
      host: this.host,
      apiKey: this.apiKey ? `${this.apiKey.substring(0, 8)}...` : 'not set',
      hasLogger: !!this.logger
    };
  }
}
