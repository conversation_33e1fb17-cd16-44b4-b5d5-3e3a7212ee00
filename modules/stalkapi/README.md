# StalkApi Module

A custom Node.js module for connecting to StalkApi websocket services with built-in logging and reconnection capabilities.

## Installation

The module is installed as a local dependency in your project:

```bash
pnpm install
```

## Usage

### Basic Import

```javascript
import Stalk<PERSON><PERSON>, { TestWebsocket } from "stalk-api";
```

### Environment Variables

Make sure to set these environment variables in your `.env` file:

```env
STALKAPI_HOST=your-stalkapi-host.com
STALK_API_KEY=your-api-key-here
```

### TestWebsocket Example

```javascript
import { TestWebsocket } from "stalk-api";

// Create instance with custom logger (e.g., blessed console)
const testWs = new TestWebsocket(app.log.bind(app));

// Start the connection
testWs.start();

// Send custom messages
testWs.sendMessage({
  type: 'custom',
  data: 'Hello StalkApi!'
});

// Check connection status
console.log('Status:', testWs.getStatus());

// Stop the connection
testWs.stop();
```

### StalkApi Main Class

```javascript
import Stalk<PERSON><PERSON> from "stalk-api";

const stalkApi = new StalkApi({
  logger: app.log.bind(app) // Use your custom logger
});

// Validate configuration
if (stalkApi.validateConfig()) {
  // Create a websocket connection
  const ws = stalkApi.createWebSocket();
  ws.connect();
}
```

## Features

### TestWebsocket Class

- **Automatic connection**: Connects to `wss://${STALKAPI_HOST}/ws?apiKey=${STALK_API_KEY}`
- **Event logging**: Logs all websocket events (open, message, close, error)
- **Message handling**: Automatically handles different message types
- **Test messages**: Sends test messages to verify connection
- **Status monitoring**: Provides connection status information

### StalkWebSocket Class

- **Reconnection logic**: Automatic reconnection with configurable attempts and delays
- **Event handlers**: Comprehensive event handling for all websocket events
- **Message formatting**: Automatic JSON parsing and stringification
- **Ping/Pong handling**: Built-in ping/pong support for connection health
- **Error handling**: Robust error handling and logging

### StalkApi Main Class

- **Configuration management**: Centralized configuration with validation
- **Factory methods**: Easy creation of websocket instances
- **Logger integration**: Seamless integration with custom logging systems

## Event Handling

The TestWebsocket automatically handles these events and logs them:

- **Connection Open**: `✅ Connection established successfully`
- **Message Received**: `📨 Message received: [message data]`
- **Connection Closed**: `❌ Connection closed`
- **Errors**: `⚠️ Error occurred: [error details]`
- **Sending Messages**: `📤 Sending test message`

## Message Types

The TestWebsocket handles these message types:

- `welcome`: Welcome messages from the server
- `ping`: Ping messages (automatically responds with pong)
- `error`: Error messages from the server
- Custom message types are logged for debugging

## Configuration Options

### StalkWebSocket Options

```javascript
const ws = new StalkWebSocket({
  url: 'wss://example.com/ws',
  apiKey: 'your-api-key',
  logger: console.log,
  maxReconnectAttempts: 5,
  reconnectDelay: 3000
});
```

### StalkApi Options

```javascript
const stalkApi = new StalkApi({
  host: 'your-host.com',
  apiKey: 'your-api-key',
  logger: console.log
});
```

## Testing

Run the test file to see the module in action:

```bash
node test/stalkapi-test.js
```

This will:
1. Start the blessed terminal interface
2. Initialize the TestWebsocket
3. Connect to the StalkApi websocket
4. Log all events to the console box
5. Send periodic test messages

## Integration with Blessed Console

The module is designed to work seamlessly with the blessed console logging system:

```javascript
import App from "./src/app.js";
import { TestWebsocket } from "stalk-api";

const app = new App();
app.start();

// Use the app's logger for websocket events
const testWs = new TestWebsocket(app.log.bind(app));
testWs.start();
```

All websocket events will appear in the blessed console box with proper timestamps and formatting.
