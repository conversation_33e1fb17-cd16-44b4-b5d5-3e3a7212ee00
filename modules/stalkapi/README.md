# StalkApi Module

A custom Node.js module for connecting to StalkApi websocket services with built-in logging and reconnection capabilities.

## Installation

The module is installed as a local dependency in your project:

```bash
pnpm install
```

## Usage

### Basic Import

```javascript
import Stalk<PERSON>pi, { TestWebsocket } from "stalk-api";
```

### Environment Variables

Make sure to set these environment variables in your `.env` file:

```env
STALKAPI_HOST=your-stalkapi-host.com
STALK_API_KEY=your-api-key-here
```

### TestWebsocket Example

#### Demo Mode (Safe Testing)
```javascript
import { TestWebsocket } from "stalk-api";

// Create instance in demo mode (no real connection needed)
const testWs = new TestWebsocket(app.log.bind(app), { demoMode: true });

// Start the demo connection
testWs.start(); // Will show simulated websocket events

// Demo mode will automatically generate test messages
```

#### Real Connection Mode
```javascript
import { TestWebsocket } from "stalk-api";

// Create instance with custom logger (e.g., blessed console)
const testWs = new TestWebsocket(app.log.bind(app)); // No demoMode option

// Start the real connection (requires STALKAPI_HOST and STALK_API_KEY)
testWs.start();

// Send custom messages
testWs.sendMessage({
  type: 'subscribe',
  stream: 'kol_activity'
});

// Check connection status
console.log('Status:', testWs.getStatus());

// Stop the connection
testWs.stop();
```

### StalkApi Main Class

```javascript
import StalkApi from "stalk-api";

const stalkApi = new StalkApi({
  logger: app.log.bind(app) // Use your custom logger
});

// Validate configuration
if (stalkApi.validateConfig()) {
  // Create a websocket connection
  const ws = stalkApi.createWebSocket();
  ws.connect();
}
```

## Features

### TestWebsocket Class

- **Automatic connection**: Connects to `wss://${STALKAPI_HOST}/ws?apiKey=${STALK_API_KEY}`
- **Event logging**: Logs all websocket events (open, message, close, error)
- **Message handling**: Automatically handles different message types
- **Test messages**: Sends test messages to verify connection
- **Status monitoring**: Provides connection status information

### StalkWebSocket Class

- **Reconnection logic**: Automatic reconnection with configurable attempts and delays
- **Event handlers**: Comprehensive event handling for all websocket events
- **Message formatting**: Automatic JSON parsing and stringification
- **Ping/Pong handling**: Built-in ping/pong support for connection health
- **Error handling**: Robust error handling and logging

### StalkApi Main Class

- **Configuration management**: Centralized configuration with validation
- **Factory methods**: Easy creation of websocket instances
- **Logger integration**: Seamless integration with custom logging systems

## Event Handling

The TestWebsocket automatically handles these events and logs them:

- **Connection Open**: `✅ Connection established successfully`
- **Message Received**: `📨 Message received: [message data]`
- **Connection Closed**: `❌ Connection closed`
- **Errors**: `⚠️ Error occurred: [error details]`
- **Sending Messages**: `📤 Sending test message`

## Message Types

The TestWebsocket handles these message types:

- `welcome`: Welcome messages from the server
- `ping`: Ping messages (automatically responds with pong)
- `error`: Error messages from the server
- Custom message types are logged for debugging

## Configuration Options

### StalkWebSocket Options

```javascript
const ws = new StalkWebSocket({
  url: 'wss://example.com/ws',
  apiKey: 'your-api-key',
  logger: console.log,
  maxReconnectAttempts: 5,
  reconnectDelay: 3000
});
```

### StalkApi Options

```javascript
const stalkApi = new StalkApi({
  host: 'your-host.com',
  apiKey: 'your-api-key',
  logger: console.log
});
```

## Testing

Run the test file to see the module in action:

```bash
node test/stalkapi-test.js
```

This will:
1. Start the blessed terminal interface
2. Initialize the TestWebsocket
3. Connect to the StalkApi websocket
4. Log all events to the console box
5. Send periodic test messages

## Integration with Blessed Console

The module is designed to work seamlessly with the blessed console logging system:

```javascript
import App from "./src/app.js";
import { TestWebsocket } from "stalk-api";

const app = new App();
app.start();

// Use the app's logger for websocket events
const testWs = new TestWebsocket(app.log.bind(app));
testWs.start();
```

All websocket events will appear in the blessed console box with proper timestamps and formatting.

## Troubleshooting

### Garbled Characters in Console
If you see garbled characters in the blessed console when using real websocket connections, this is usually caused by:

1. **Large JSON messages**: The module automatically truncates messages longer than 150 characters
2. **Terminal rendering issues**: Try using demo mode first to test the integration
3. **Message encoding**: Some websocket messages may contain special characters

**Solution**: Use demo mode for testing the integration:
```javascript
const testWs = new TestWebsocket(app.log.bind(app), { demoMode: true });
```

### Connection Issues
- Ensure `STALKAPI_HOST` and `STALK_API_KEY` are set in your `.env` file
- Check that your API key has websocket permissions
- Use demo mode to test the module without real connections

### Message Types
The module handles these StalkApi message types:
- `connected`: Connection established
- `subscribed`: Stream subscription confirmed
- `stream_data`: Real-time data from subscribed streams
- `ping`/`pong`: Connection health checks
- `error`: Server error messages
