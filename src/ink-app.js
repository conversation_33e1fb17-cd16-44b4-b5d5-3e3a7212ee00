import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';

const App = () => {
  const [logs, setLogs] = useState([]);
  const [stalkApi, setStalkApi] = useState(null);
  const [testWebsocket, setTestWebsocket] = useState(null);

  // Add log function
  const addLog = useCallback((...args) => {
    const timestamp = new Date().toLocaleTimeString();
    const message = `[${timestamp}] ${args.map(arg => 
      typeof arg === 'object' && arg !== null 
        ? JSON.stringify(arg, null, 2) 
        : String(arg)
    ).join(' ')}`;
    
    setLogs(prev => {
      const newLogs = [...prev, message];
      // Keep only last 50 messages for performance
      return newLogs.slice(-50);
    });
  }, []);

  // Clear logs function
  const clearLogs = useCallback(() => {
    setLogs([]);
    addLog('Console cleared');
  }, [addLog]);

  // Initialize StalkAPI and websocket
  useEffect(() => {
    const initializeApp = async () => {
      try {
        addLog('Application started successfully!');
        
        // Import StalkAPI module
        const { default: StalkApi, TestWebsocket } = await import('stalk-api');
        
        // Create StalkAPI configuration
        const config = {
          host: process.env.STALKAPI_HOST || 'data.stalkapi.com',
          apiKey: process.env.STALK_API_KEY || 'VkD2wwlC...',
          hasLogger: true
        };
        
        addLog('StalkApi configuration:', config);
        
        // Create user data example
        const userData = {
          user: "john_doe",
          status: "active",
          permissions: ["read", "write"],
          sessionCount: 42
        };
        
        addLog('User data:', userData);
        
        // Create StalkApi instance
        const stalkApiInstance = new StalkApi({ logger: addLog });
        setStalkApi(stalkApiInstance);
        
        addLog('Creating TestWebsocket instance...');
        addLog('Starting TestWebsocket in demo mode...');
        
        // Create TestWebsocket instance with demo mode enabled
        const testWs = new TestWebsocket(addLog, { demoMode: true });
        setTestWebsocket(testWs);

        // Start the websocket connection
        testWs.start();
        
      } catch (error) {
        addLog('Error initializing application:', error.message);
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      if (testWebsocket) {
        testWebsocket.stop();
      }
    };
  }, [addLog]);

  // Handle keyboard input
  useInput((input, key) => {
    if (input === 'q' || (key.ctrl && input === 'c')) {
      // Cleanup and exit
      if (testWebsocket) {
        testWebsocket.stop();
      }
      process.exit(0);
    } else if (input === 'c') {
      clearLogs();
    }
  });

  return React.createElement(Box, { flexDirection: "column", height: "100%" },
    // Header
    React.createElement(Box, { borderStyle: "single", borderColor: "cyan", padding: 1 },
      React.createElement(Text, { color: "cyan", bold: true },
        "StalkAPI Monitor - Press 'q' to quit, 'c' to clear console"
      )
    ),

    // Main content area
    React.createElement(Box, { flexGrow: 1, flexDirection: "row" },
      // Left panels
      React.createElement(Box, { flexDirection: "column", width: "60%" },
        // Top Left
        React.createElement(Box, { borderStyle: "single", borderColor: "white", padding: 1, height: "50%" },
          React.createElement(Text, null, "Top Left Panel")
        ),

        // Top Center
        React.createElement(Box, { borderStyle: "single", borderColor: "white", padding: 1, height: "50%" },
          React.createElement(Text, null, "Top Center Panel")
        )
      ),

      // Console Log (Right panel)
      React.createElement(Box, {
        borderStyle: "single",
        borderColor: "cyan",
        padding: 1,
        width: "40%",
        flexDirection: "column"
      },
        React.createElement(Text, { color: "cyan", bold: true }, "Console Log"),
        React.createElement(Box, { flexDirection: "column", flexGrow: 1, overflowY: "hidden" },
          ...logs.map((log, index) =>
            React.createElement(Text, { key: index, wrap: "wrap" }, log)
          )
        )
      )
    ),

    // Bottom panel
    React.createElement(Box, { borderStyle: "single", borderColor: "white", padding: 1, height: 10 },
      React.createElement(Text, null, "Bottom Panel")
    )
  );
};

// Export the component for rendering
export default App;
