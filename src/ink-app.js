import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useStdout } from 'ink';

const App = () => {
  const [logs, setLogs] = useState([]);
  const [stalkApi, setStalkApi] = useState(null);
  const [testWebsocket, setTestWebsocket] = useState(null);

  // Add log function with better line management
  const addLog = useCallback((...args) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntries = args.map(arg =>
      typeof arg === 'object' && arg !== null
        ? JSON.stringify(arg, null, 2)
        : String(arg)
    );

    const message = `[${timestamp}] ${logEntries.join(' ')}`;

    // Split message into lines that fit the console width
    const maxWidth = 45; // Console panel width minus padding
    const lines = [];

    message.split('\n').forEach(line => {
      if (line.length <= maxWidth) {
        lines.push(line);
      } else {
        // Break long lines at word boundaries when possible
        const words = line.split(' ');
        let currentLine = '';

        words.forEach(word => {
          if ((currentLine + ' ' + word).length <= maxWidth) {
            currentLine = currentLine ? currentLine + ' ' + word : word;
          } else {
            if (currentLine) lines.push(currentLine);
            currentLine = word;
          }
        });

        if (currentLine) lines.push(currentLine);
      }
    });

    setLogs(prev => {
      const newLogs = [...prev, ...lines];
      // Keep only last 100 lines for performance
      return newLogs.slice(-100);
    });
  }, []);

  // Clear logs function
  const clearLogs = useCallback(() => {
    setLogs([]);
    addLog('Console cleared');
  }, [addLog]);

  // Initialize StalkAPI and websocket
  useEffect(() => {
    const initializeApp = async () => {
      try {
        addLog('Application started successfully!');
        
        // Import StalkAPI module
        const { default: StalkApi, TestWebsocket } = await import('stalk-api');
        
        // Create StalkAPI configuration
        const config = {
          host: process.env.STALKAPI_HOST || 'data.stalkapi.com',
          apiKey: process.env.STALK_API_KEY || 'VkD2wwlC...',
          hasLogger: true
        };
        
        addLog('StalkApi configuration:', config);
        
        // Create user data example
        const userData = {
          user: "john_doe",
          status: "active",
          permissions: ["read", "write"],
          sessionCount: 42
        };
        
        addLog('User data:', userData);
        
        // Create StalkApi instance
        const stalkApiInstance = new StalkApi({ logger: addLog });
        setStalkApi(stalkApiInstance);
        
        addLog('Creating TestWebsocket instance...');
        addLog('Starting TestWebsocket in demo mode...');
        
        // Create TestWebsocket instance with demo mode enabled
        const testWs = new TestWebsocket(addLog, { demoMode: true });
        setTestWebsocket(testWs);

        // Start the websocket connection
        testWs.start();
        
      } catch (error) {
        addLog('Error initializing application:', error.message);
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      if (testWebsocket) {
        testWebsocket.stop();
      }
    };
  }, [addLog]);

  // Handle keyboard input
  useInput((input, key) => {
    if (input === 'q' || (key.ctrl && input === 'c')) {
      // Cleanup and exit
      if (testWebsocket) {
        testWebsocket.stop();
      }
      process.exit(0);
    } else if (input === 'c') {
      clearLogs();
    }
  });

  return React.createElement(Box, {
    flexDirection: "column",
    width: "100%",
    height: "100%",
    minHeight: "100vh"
  },
    // Header - Fixed height
    React.createElement(Box, {
      borderStyle: "single",
      borderColor: "cyan",
      padding: 1,
      minHeight: 3,
      maxHeight: 3
    },
      React.createElement(Text, { color: "cyan", bold: true },
        "StalkAPI Monitor - Press 'q' to quit, 'c' to clear console"
      )
    ),

    // Main content area - Takes remaining space
    React.createElement(Box, {
      flexDirection: "row",
      flexGrow: 1,
      minHeight: 0
    },
      // Left panels - 60% width
      React.createElement(Box, {
        flexDirection: "column",
        width: "60%",
        minWidth: "60%",
        maxWidth: "60%"
      },
        // Top Left - 50% of left area
        React.createElement(Box, {
          borderStyle: "single",
          borderColor: "white",
          padding: 1,
          height: "50%",
          minHeight: "50%",
          maxHeight: "50%"
        },
          React.createElement(Text, null, "Top Left Panel")
        ),

        // Top Center - 50% of left area
        React.createElement(Box, {
          borderStyle: "single",
          borderColor: "white",
          padding: 1,
          height: "50%",
          minHeight: "50%",
          maxHeight: "50%"
        },
          React.createElement(Text, null, "Top Center Panel")
        )
      ),

      // Console Log (Right panel) - 40% width, full height
      React.createElement(Box, {
        borderStyle: "single",
        borderColor: "cyan",
        padding: 1,
        width: "40%",
        minWidth: "40%",
        maxWidth: "40%",
        flexDirection: "column",
        height: "100%"
      },
        React.createElement(Text, { color: "cyan", bold: true }, "Console Log"),
        React.createElement(Box, {
          flexDirection: "column",
          flexGrow: 1,
          overflow: "hidden",
          minHeight: 0
        },
          // Show logs that fit in available space
          ...logs.slice(-25).map((log, index) =>
            React.createElement(Text, {
              key: `log-${logs.length - 25 + index}`,
              wrap: "wrap"
            }, log)
          )
        )
      )
    ),

    // Bottom panel - Fixed height
    React.createElement(Box, {
      borderStyle: "single",
      borderColor: "white",
      padding: 1,
      minHeight: 8,
      maxHeight: 8
    },
      React.createElement(Text, null, "Bottom Panel")
    )
  );
};

// Export the component for rendering
export default App;
