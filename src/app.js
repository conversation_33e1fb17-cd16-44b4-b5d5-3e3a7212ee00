import blessed from "blessed";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    this.topLeftBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    this.topCenterBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    this.bottomBox = blessed.box({
      width: "100%",
      height: "50%",
      top: "50%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Bottom"
    });

    // Create console list (top right) - use list for better dynamic content handling
    this.consoleBox = blessed.list({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      label: " Console Log ",
      items: [],
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      style: {
        border: {
          fg: "cyan"
        },
        selected: {
          bg: "blue"
        }
      }
    });

    // Append all boxes to screen
    this.screen.append(this.topLeftBox);
    this.screen.append(this.topCenterBox);
    this.screen.append(this.consoleBox);
    this.screen.append(this.bottomBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function that displays in the console box
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments similar to console.log - show actual JSON but make it compact for single lines
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Use compact JSON for better display in scrollable text
          return JSON.stringify(arg);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Add to log messages array
    if (!this.logMessages) {
      this.logMessages = [];
    }

    this.logMessages.push(message);

    // Keep only last 100 messages to prevent memory issues
    if (this.logMessages.length > 100) {
      this.logMessages = this.logMessages.slice(-100);
    }

    this.updateConsoleDisplay();
  }

  // Update the console display
  updateConsoleDisplay() {
    if (this.consoleBox) {
      const content = this.logMessages.join('\n');
      this.consoleBox.setContent(content);
      this.consoleBox.setScrollPerc(100); // Auto-scroll to bottom
      this.screen.render();
    }
  }

  // Clear the console log
  clearConsole() {
    if (this.logMessages) {
      this.logMessages = [];
      this.updateConsoleDisplay();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }



  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.clearConsole();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });
  }
}