import blessed from "blessed";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,      // Recommended by blessed docs for better rendering
      useBCE: true,        // Enable back_color_erase optimizations
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    this.topLeftBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    this.topCenterBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    this.bottomBox = blessed.box({
      width: "100%",
      height: "50%",
      top: "50%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Bottom"
    });

    // Create console log widget (top right) - using blessed.log for proper streaming text
    this.consoleBox = blessed.log({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      label: " Console Log ",
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollback: 1000,  // Keep more history for better scrolling
      style: {
        border: {
          fg: "cyan"
        }
      }
    });

    // Append all boxes to screen
    this.screen.append(this.topLeftBox);
    this.screen.append(this.topCenterBox);
    this.screen.append(this.consoleBox);
    this.screen.append(this.bottomBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function using blessed.log widget for proper streaming
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments like console.log with proper JSON formatting
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Use proper JSON formatting with indentation for readability
          return JSON.stringify(arg, null, 2);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Use blessed.log's built-in logging which handles content properly
    if (this.consoleBox && this.consoleBox.log) {
      this.consoleBox.log(message);

      // Use a small delay to batch rapid updates and reduce rendering conflicts
      if (!this.renderTimeout) {
        this.renderTimeout = setTimeout(() => {
          this.screen.render();
          this.renderTimeout = null;
        }, 16); // ~60fps rendering rate
      }
    }
  }







  // Clear the console log
  clearConsole() {
    if (this.consoleBox && this.consoleBox.clear) {
      this.consoleBox.clear();
      this.screen.render();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }



  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.clearConsole();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });
  }
}