import blessed from "blessed";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    this.topLeftBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    this.topCenterBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    this.bottomBox = blessed.box({
      width: "100%",
      height: "50%",
      top: "50%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Bottom"
    });

    // Create console box (top right) - simple box with debounced updates
    this.consoleBox = blessed.box({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      label: " Console Log ",
      content: "",
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      wrap: false,
      style: {
        border: {
          fg: "cyan"
        }
      }
    });

    // Initialize log buffer and debounce system
    this.logBuffer = [];
    this.updatePending = false;

    // Append all boxes to screen
    this.screen.append(this.topLeftBox);
    this.screen.append(this.topCenterBox);
    this.screen.append(this.consoleBox);
    this.screen.append(this.bottomBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function with debounced updates to prevent rendering conflicts
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments with compact JSON formatting to fit console width
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Use compact JSON formatting that fits in the console width
          const compact = JSON.stringify(arg);
          if (compact.length <= 60) {
            return compact;
          }
          // For longer objects, show key structure
          const keys = Object.keys(arg);
          if (keys.length <= 4) {
            return `{${keys.join(', ')}}`;
          }
          return `{${keys.slice(0, 3).join(', ')}, +${keys.length - 3} more}`;
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Add to log buffer
    this.logBuffer.push(message);

    // Keep only last 30 messages for better performance
    if (this.logBuffer.length > 30) {
      this.logBuffer = this.logBuffer.slice(-30);
    }

    // Use debounced update to prevent rendering conflicts
    this.scheduleUpdate();
  }

  // Debounced update system to prevent blessed rendering conflicts
  scheduleUpdate() {
    if (this.updatePending) {
      return; // Update already scheduled
    }

    this.updatePending = true;

    // Use setImmediate to batch updates and prevent conflicts
    setImmediate(() => {
      if (this.consoleBox) {
        const content = this.logBuffer.join('\n');
        this.consoleBox.setContent(content);
        this.consoleBox.setScrollPerc(100); // Auto-scroll to bottom
        this.screen.render();
      }
      this.updatePending = false;
    });
  }





  // Clear the console log
  clearConsole() {
    this.logBuffer = [];
    if (this.consoleBox) {
      this.consoleBox.setValue('');
      this.screen.render();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }



  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.clearConsole();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });
  }
}