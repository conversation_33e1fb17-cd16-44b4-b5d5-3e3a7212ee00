import blessed from "blessed";
import { createGrid } from "./ui/blessed.js";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });
  }

  start() {
    createGrid(this.screen, {
      width: "50%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    createGrid(this.screen, {
      width: "50%",
      height: "50%",
      top: "0%",
      left: "50%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Right"
    });

    this.screen.render();
  }

  stop() {
    this.screen.destroy();
  }

  
}