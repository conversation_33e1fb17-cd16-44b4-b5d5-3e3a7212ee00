import blessed from "blessed";
import { createGrid } from "./ui/blessed.js";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    createGrid(this.screen, {
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    createGrid(this.screen, {
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    // Create console log box (top right) - use log widget instead of box for better text handling
    this.consoleBox = blessed.log({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      tags: true,
      label: " Console Log ",
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollback: 100, // Keep last 100 lines
      style: {
        border: {
          fg: "cyan"
        }
      }
    });

    this.screen.append(this.consoleBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function that displays in the console box
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments similar to console.log - show actual JSON but formatted nicely
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Show actual JSON but make it more readable
          return JSON.stringify(arg, null, 2);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Use blessed log widget's built-in add method instead of managing content manually
    if (this.consoleBox) {
      this.consoleBox.log(message);
      this.screen.render();
    }
  }

  // Clear the console log
  clearConsole() {
    if (this.consoleBox) {
      this.consoleBox.setContent('');
      this.screen.render();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }



  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.clearConsole();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });
  }
}