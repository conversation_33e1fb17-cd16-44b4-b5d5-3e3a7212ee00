import blessed from "blessed";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    this.topLeftBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    this.topCenterBox = blessed.box({
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    this.bottomBox = blessed.box({
      width: "100%",
      height: "50%",
      top: "50%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Bottom"
    });

    // Create console box (top right) - custom solution for multi-line JSON display
    this.consoleBox = blessed.box({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      label: " Console Log ",
      content: "",
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      wrap: false,
      style: {
        border: {
          fg: "cyan"
        }
      }
    });

    // Append all boxes to screen
    this.screen.append(this.topLeftBox);
    this.screen.append(this.topCenterBox);
    this.screen.append(this.consoleBox);
    this.screen.append(this.bottomBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function that displays in the console box with proper JSON formatting
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments like console.log but format for terminal display
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Format JSON to fit in console box (approximately 50 chars wide)
          return this.formatJsonForConsole(arg);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Add to log messages array
    if (!this.logMessages) {
      this.logMessages = [];
    }

    // Split message into lines that fit the console width
    const lines = this.splitIntoLines(message, 48); // Leave some margin
    this.logMessages.push(...lines);

    // Keep only last 100 messages to prevent memory issues
    if (this.logMessages.length > 100) {
      this.logMessages = this.logMessages.slice(-100);
    }

    this.updateConsoleDisplay();
  }

  // Format JSON for console display with proper line breaks
  formatJsonForConsole(obj) {
    // For simple objects, try compact format first
    const compact = JSON.stringify(obj);
    if (compact.length <= 45) {
      return compact;
    }

    // For complex objects, show actual structure but formatted for console
    const formatted = JSON.stringify(obj, null, 2);
    const lines = formatted.split('\n');

    // If formatted version has reasonable line lengths, use it
    const maxLineLength = Math.max(...lines.map(line => line.length));
    if (maxLineLength <= 45 && lines.length <= 8) {
      return formatted;
    }

    // Otherwise, create a readable but compact format that shows actual data
    return this.createCompactJson(obj);
  }

  // Create a compact JSON representation that shows actual content
  createCompactJson(obj, depth = 0) {
    if (depth > 2) return '[Object]'; // Prevent deep nesting

    if (Array.isArray(obj)) {
      if (obj.length === 0) return '[]';
      if (obj.length <= 2) {
        const items = obj.map(item => this.createCompactJson(item, depth + 1));
        const result = '[' + items.join(', ') + ']';
        return result.length <= 40 ? result : `[${obj.length} items]`;
      }
      return `[${obj.length} items]`;
    }

    if (typeof obj === 'object' && obj !== null) {
      const keys = Object.keys(obj);
      if (keys.length === 0) return '{}';

      // Show key-value pairs for important fields
      const importantKeys = keys.slice(0, 2); // Show first 2 keys
      const pairs = importantKeys.map(key => {
        let value = obj[key];

        // Format different types appropriately
        if (typeof value === 'string') {
          value = value.length > 15 ? `"${value.substring(0, 12)}..."` : `"${value}"`;
        } else if (typeof value === 'object' && value !== null) {
          value = this.createCompactJson(value, depth + 1);
        } else {
          value = JSON.stringify(value);
        }

        return `${key}: ${value}`;
      });

      let result = '{' + pairs.join(', ');
      if (keys.length > 2) {
        result += `, +${keys.length - 2} more`;
      }
      result += '}';

      return result.length <= 45 ? result : `{${keys.join(', ')}}`;
    }

    return JSON.stringify(obj);
  }

  // Split message into lines that fit the console width
  splitIntoLines(message, maxWidth) {
    if (message.length <= maxWidth) {
      return [message];
    }

    const lines = [];
    let currentLine = '';
    const words = message.split(' ');

    for (const word of words) {
      if ((currentLine + ' ' + word).length <= maxWidth) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        // If single word is too long, split it
        if (word.length > maxWidth) {
          let remaining = word;
          while (remaining.length > maxWidth) {
            lines.push(remaining.substring(0, maxWidth - 3) + '...');
            remaining = '...' + remaining.substring(maxWidth - 3);
          }
          currentLine = remaining;
        } else {
          currentLine = word;
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [message.substring(0, maxWidth)];
  }

  // Update the console display
  updateConsoleDisplay() {
    if (this.consoleBox) {
      const content = this.logMessages.join('\n');
      this.consoleBox.setContent(content);
      this.consoleBox.setScrollPerc(100); // Auto-scroll to bottom
      this.screen.render();
    }
  }

  // Clear the console log
  clearConsole() {
    if (this.logMessages) {
      this.logMessages = [];
      this.updateConsoleDisplay();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }



  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.clearConsole();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });
  }
}