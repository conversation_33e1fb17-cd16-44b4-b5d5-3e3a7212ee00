import blessed from "blessed";
import { createGrid } from "./ui/blessed.js";

export default class App {
  constructor() {
    this.screen = blessed.screen({
      fullUnicode: true,
      smartCSR: true,
      cursor: {
        artificial: true,
      },
    });

    this.consoleBox = null;
    this.logMessages = [];
    this.dateTimeInterval = null;
  }

  start() {
    createGrid(this.screen, {
      width: "30%",
      height: "50%",
      top: "0%",
      left: "0%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Left"
    });

    createGrid(this.screen, {
      width: "30%",
      height: "50%",
      top: "0%",
      left: "30%",
      border: {
        type: "line",
      },
      tags: true,
      content: "Top Center"
    });

    // Create console log box (top right)
    this.consoleBox = blessed.box({
      width: "40%",
      height: "50%",
      top: "0%",
      left: "60%",
      border: {
        type: "line",
      },
      tags: true,
      label: " Console Log ",
      content: "",
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      wrap: false, // Disable word wrapping to prevent line issues
      style: {
        border: {
          fg: "cyan"
        }
      }
    });

    this.screen.append(this.consoleBox);

    // Set up console logging functionality
    this.setupConsoleLogging();

    // Set up keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Start datetime logging every second
    // this.startDateTimeLogging();

    this.screen.render();
  }

  stop() {
    // Clear the datetime interval
    if (this.dateTimeInterval) {
      clearInterval(this.dateTimeInterval);
    }
    this.screen.destroy();
  }

  // Custom log function that displays in the console box
  log(...args) {
    const timestamp = new Date().toLocaleTimeString();
    let message = `[${timestamp}] `;

    // Process arguments similar to console.log with better formatting
    const processedArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          // Create a more compact JSON representation
          const jsonStr = JSON.stringify(arg, null, 2);
          // Break long JSON into multiple lines but keep each line short
          return this.formatJsonForConsole(jsonStr);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    });

    message += processedArgs.join(' ');

    // Split long messages into multiple shorter lines
    const lines = this.splitMessageIntoLines(message);
    this.logMessages.push(...lines);

    // Keep only last 100 messages to prevent memory issues
    if (this.logMessages.length > 100) {
      this.logMessages = this.logMessages.slice(-100);
    }

    this.updateConsoleDisplay();
  }

  // Update the console box display
  updateConsoleDisplay() {
    if (this.consoleBox) {
      // Clear the box first to prevent rendering artifacts
      this.consoleBox.setContent('');

      // Set the new content
      const content = this.logMessages.join('\n');
      this.consoleBox.setContent(content);

      // Auto-scroll to bottom
      this.consoleBox.setScrollPerc(100);

      // Force a full render
      this.screen.render();
    }
  }

  // Set up console logging functionality
  setupConsoleLogging() {
    // Make the log function available globally if needed
    global.log = this.log.bind(this);
  }

  // Start logging datetime every second
  startDateTimeLogging() {
    this.dateTimeInterval = setInterval(() => {
      const now = new Date();
      this.log('Current time:', now.toLocaleString());
    }, 1000);
  }

  // Helper function to format JSON for console display
  formatJsonForConsole(jsonStr) {
    // If JSON is short, return as is
    if (jsonStr.length <= 60) {
      return jsonStr.replace(/\n/g, ' ').replace(/\s+/g, ' ');
    }

    // For longer JSON, create a more compact format
    try {
      const obj = JSON.parse(jsonStr);
      return this.compactJsonString(obj);
    } catch (e) {
      return jsonStr.substring(0, 60) + '...';
    }
  }

  // Create a compact JSON string representation
  compactJsonString(obj, maxLength = 60) {
    if (typeof obj !== 'object' || obj === null) {
      return String(obj);
    }

    if (Array.isArray(obj)) {
      if (obj.length === 0) return '[]';
      const items = obj.slice(0, 3).map(item => this.compactJsonString(item, 20));
      const result = '[' + items.join(', ') + (obj.length > 3 ? ', ...' : '') + ']';
      return result.length > maxLength ? '[Array(' + obj.length + ')]' : result;
    }

    const keys = Object.keys(obj);
    if (keys.length === 0) return '{}';

    const pairs = keys.slice(0, 3).map(key => {
      const value = this.compactJsonString(obj[key], 15);
      return `${key}: ${value}`;
    });

    const result = '{' + pairs.join(', ') + (keys.length > 3 ? ', ...' : '') + '}';
    return result.length > maxLength ? '{Object}' : result;
  }

  // Split long messages into multiple lines that fit the console width
  splitMessageIntoLines(message, maxWidth = 50) {
    if (message.length <= maxWidth) {
      return [message];
    }

    const lines = [];
    let currentLine = '';
    const words = message.split(' ');

    for (const word of words) {
      if ((currentLine + ' ' + word).length <= maxWidth) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        // If single word is too long, truncate it
        if (word.length > maxWidth) {
          lines.push(word.substring(0, maxWidth - 3) + '...');
          currentLine = '';
        } else {
          currentLine = word;
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [message.substring(0, maxWidth)];
  }

  // Set up keyboard shortcuts
  setupKeyboardShortcuts() {
    // Quit application with 'q' or Ctrl+C
    this.screen.key(['q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    // Clear console log with 'c'
    this.screen.key(['c'], () => {
      this.logMessages = [];
      this.updateConsoleDisplay();
      this.log('Console cleared');
    });

    // Focus on console box with 'l' (for log)
    this.screen.key(['l'], () => {
      this.consoleBox.focus();
      this.screen.render();
    });

    // Refresh display with 'r'
    this.screen.key(['r'], () => {
      this.updateConsoleDisplay();
    });
  }
}